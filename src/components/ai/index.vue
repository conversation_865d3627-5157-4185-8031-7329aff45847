<template>
  <!-- 主布局容器 -->
  <div :style="styles.layout">
    <!-- 左侧菜单栏 -->
    <div :style="styles.menu">
      <!-- 🌟 Logo区域 -->
      <div :style="styles.logo">
        <img
            src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*eco6RrQhxbMAAAAAAAAAAAAADgCCAQ/original"
            draggable="false"
            alt="logo"
            :style="styles['logo-img']"
        >
        <span :style="styles['logo-span']">Ant Design X Vue</span>
      </div>

      <!-- 🌟 新建对话按钮 -->
      <Button
          type="link"
          :style="styles.addBtn"
          @click="onAddConversation"
      >
        <PlusOutlined/>
        New Conversation
      </Button>

      <!-- 🌟 对话列表管理组件 -->
      <Conversations
          :items="conversationsItems"
          :style="styles.conversations"
          :active-key="activeKey"
          @active-change="onConversationClick"
      />
    </div>

    <!-- 右侧聊天主区域 -->
    <div :style="styles.chat">
      <!-- 🌟 消息气泡列表 - 显示所有对话消息 -->
      <Bubble.List
          :items="items"
          :roles="roles"
          :style="styles.messages"
      />

      <!-- 🌟 快捷提示词组件 - 提供预设的对话选项 -->
      <Prompts
          :items="senderPromptsItems"
          @item-click="onPromptsItemClick"
      />

      <!-- 🌟 消息输入框组件 -->
      <Sender
          :value="content"
          :style="styles.sender"
          :loading="agentRequestLoading"
          @submit="onSubmit"
          @change="value => content = value"
      >
        <!-- 输入框前缀：文件附件按钮 -->
        <template #prefix>
          <Badge :dot="attachedFiles.length > 0 && !headerOpen">
            <Button
                type="text"
                @click="() => headerOpen = !headerOpen"
            >
              <template #icon>
                <PaperClipOutlined/>
              </template>
            </Button>
          </Badge>
        </template>

        <!-- 输入框头部：文件上传区域 -->
        <template #header>
          <Sender.Header
              title="Attachments"
              :open="headerOpen"
              :styles="{ content: { padding: 0 } }"
              @open-change="open => headerOpen = open"
          >
            <!-- 文件附件组件 -->
            <Attachments
                :before-upload="() => false"
                :items="attachedFiles"
                @change="handleFileChange"
            >
              <!-- 文件上传占位符 -->
              <template #placeholder="type">
                <Flex
                    v-if="type && type.type === 'inline'"
                    align="center"
                    justify="center"
                    vertical
                    gap="2"
                >
                  <Typography.Text style="font-size: 30px; line-height: 1;">
                    <CloudUploadOutlined/>
                  </Typography.Text>
                  <Typography.Title
                      :level="5"
                      style="margin: 0; font-size: 14px; line-height: 1.5;"
                  >
                    Upload files
                  </Typography.Title>
                  <Typography.Text type="secondary">
                    Click or drag files to this area to upload
                  </Typography.Text>
                </Flex>
                <Typography.Text v-if="type && type.type === 'drop'">
                  Drop file here
                </Typography.Text>
              </template>
            </Attachments>
          </Sender.Header>
        </template>
      </Sender>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入类型定义
import {
  type AttachmentsProps,
  type BubbleListProps,
  type ConversationsProps,
  type PromptsProps
} from 'ant-design-x-vue'
import type {VNode} from 'vue'

// 导入图标组件
import {
  CloudUploadOutlined,
  CommentOutlined,
  EllipsisOutlined,
  FireOutlined,
  HeartOutlined,
  PaperClipOutlined,
  PlusOutlined,
  ReadOutlined,
  ShareAltOutlined,
  SmileOutlined,
  UserOutlined,
} from '@ant-design/icons-vue'

// 导入Ant Design Vue组件
import {Badge, Button, Flex, Space, Typography, theme} from 'ant-design-vue'

// 导入Ant Design X Vue AI组件
import {
  Attachments,
  Bubble,
  Conversations,
  Prompts,
  Sender,
  Welcome,
} from 'ant-design-x-vue'

// 导入Vue组合式API
import {computed, h, ref, watch} from 'vue'

// 导入markdown-it
import markdownit from 'markdown-it'

// 获取主题token，用于样式计算
const {token} = theme.useToken()

// ==================== Markdown配置 ====================
// 初始化markdown-it实例，支持HTML和换行
const md = markdownit({
  html: true,
  breaks: true,
  // 可选：添加代码高亮等插件
  // highlight: function (str, lang) {
  //   if (lang && hljs.getLanguage(lang)) {
  //     try {
  //       return hljs.highlight(str, { language: lang }).value;
  //     } catch (__) {}
  //   }
  //   return ''; // 使用默认的转义
  // }
})

// ==================== Markdown渲染函数 ====================
// 自定义消息渲染函数，支持Markdown格式
const renderMarkdown = (content: string) => {
  // 检查内容是否包含Markdown语法
  const hasMarkdown = /[*_`#\[\]()>|]/.test(content)

  if (hasMarkdown) {
    return h(Typography, null, {
      default: () => h('div', {
        innerHTML: md.render(content),
        style: {
          // Markdown样式优化
          'line-height': '1.6',
          'word-wrap': 'break-word',
        }
      }),
    })
  }
  // 没有Markdown语法时直接返回文本内容
  return content
}

// 渲染用户消息（包含附件信息）
const renderUserMessage = (message: string, attachments?: Array<{name: string, size: number, type: string}>) => {
  if (!attachments || attachments.length === 0) {
    return message
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  return h('div', {}, [
    // 文本消息
    h('div', { style: { marginBottom: '8px' } }, message),
    // 附件信息
    h('div', {
      style: {
        padding: '8px 12px',
        backgroundColor: '#f5f5f5',
        borderRadius: '6px',
        fontSize: '12px',
        color: '#666'
      }
    }, [
      h('div', { style: { fontWeight: 'bold', marginBottom: '4px' } }, `📎 附件 (${attachments.length}个):`),
      ...attachments.map(file =>
        h('div', {
          key: file.name,
          style: {
            display: 'flex',
            justifyContent: 'space-between',
            marginBottom: '2px'
          }
        }, [
          h('span', {}, file.name),
          h('span', {}, formatFileSize(file.size))
        ])
      )
    ])
  ])
}

// ==================== 样式定义 ====================
// 使用computed计算样式，确保主题变化时样式自动更新
const styles = computed(() => {
  return {
    // 主布局样式
    'layout': {
      'width': '100%',
      'min-width': '970px',
      'height': '722px',
      'border-radius': `${token.value.borderRadius}px`,
      'display': 'flex',
      'background': `${token.value.colorBgContainer}`,
      'font-family': `AlibabaPuHuiTi, ${token.value.fontFamily}, sans-serif`,
    },
    // 左侧菜单样式
    'menu': {
      'background': `${token.value.colorBgLayout}80`,
      'width': '280px',
      'height': '100%',
      'display': 'flex',
      'flex-direction': 'column',
    },
    // 对话列表样式
    'conversations': {
      'padding': '0 12px',
      'flex': 1,
      'overflow-y': 'auto',
    },
    // 聊天区域样式
    'chat': {
      'height': '100%',
      'width': '100%',
      'max-width': '700px',
      'margin': '0 auto',
      'box-sizing': 'border-box',
      'display': 'flex',
      'flex-direction': 'column',
      'padding': `${token.value.paddingLG}px`,
      'gap': '16px',
    },
    // 消息列表样式
    'messages': {
      flex: 1,
    },
    // 占位符样式
    'placeholder': {
      'padding-top': '32px',
      'text-align': 'left',
      'flex': 1,
    },
    // 输入框样式
    'sender': {
      'box-shadow': token.value.boxShadow,
    },
    // Logo样式
    'logo': {
      'display': 'flex',
      'height': '72px',
      'align-items': 'center',
      'justify-content': 'start',
      'padding': '0 24px',
      'box-sizing': 'border-box',
    },
    'logo-img': {
      width: '24px',
      height: '24px',
      display: 'inline-block',
    },
    'logo-span': {
      'display': 'inline-block',
      'margin': '0 8px',
      'font-weight': 'bold',
      'color': token.value.colorText,
      'font-size': '16px',
    },
    // 新建对话按钮样式
    'addBtn': {
      background: '#1677ff0f',
      border: '1px solid #1677ff34',
      width: 'calc(100% - 24px)',
      margin: '0 12px 24px 12px',
    },
  } as const
})

// 定义组件名称
defineOptions({name: 'PlaygroundIndependentSetup'})

// ==================== 工具函数 ====================
// 渲染带图标的标题
function renderTitle(icon: VNode, title: string) {
  return h(Space, {align: 'start'}, () => [icon, h('span', title)])
}

// ==================== 初始数据 ====================
// 默认对话列表
const defaultConversationsItems = [
  {
    key: '0',
    label: 'What is Ant Design X?',
  },
]

// 欢迎页面的提示词选项
const placeholderPromptsItems: PromptsProps['items'] = [
  {
    key: '1',
    label: renderTitle(h(FireOutlined, {style: {color: '#FF4D4F'}}), 'Hot Topics'),
    description: 'What are you interested in?',
    children: [
      {
        key: '1-1',
        description: `What's new in X?`,
      },
      {
        key: '1-2',
        description: `What's AGI?`,
      },
      {
        key: '1-3',
        description: `Where is the doc?`,
      },
    ],
  },
  {
    key: '2',
    label: renderTitle(h(ReadOutlined, {style: {color: '#1890FF'}}), 'Design Guide'),
    description: 'How to design a good product?',
    children: [
      {
        key: '2-1',
        icon: h(HeartOutlined),
        description: `Know the well`,
      },
      {
        key: '2-2',
        icon: h(SmileOutlined),
        description: `Set the AI role`,
      },
      {
        key: '2-3',
        icon: h(CommentOutlined),
        description: `Express the feeling`,
      },
    ],
  },
]

// 输入框下方的快捷提示词
const senderPromptsItems: PromptsProps['items'] = [
  {
    key: '1',
    description: 'Hot Topics',
    icon: h(FireOutlined, {style: {color: '#FF4D4F'}}),
  },
  {
    key: '2',
    description: 'Design Guide',
    icon: h(ReadOutlined, {style: {color: '#1890FF'}}),
  },
]

// ==================== 消息角色配置 ====================
// 定义AI和用户消息的显示样式
const roles: BubbleListProps['roles'] = {
  // AI消息样式配置
  ai: {
    placement: 'start', // 左对齐
    avatar: {icon: h(UserOutlined), style: {background: '#fde3cf'}},
    typing: {step: 5, interval: 20}, // 打字机效果配置
    styles: {
      content: {
        borderRadius: '16px', // 圆角
      },
    },
  },
  // 用户消息样式配置
  local: {
    placement: 'end', // 右对齐
    avatar: {icon: h(UserOutlined), style: {background: '#87d068'}},
    variant: 'shadow', // 阴影样式
  },
}

// ==================== 响应式状态 ====================
// 文件上传区域是否展开
const headerOpen = ref(false)
// 输入框内容
const content = ref('')
// 对话列表数据
const conversationsItems = ref(defaultConversationsItems)
// 当前激活的对话key
const activeKey = ref(defaultConversationsItems[0].key)
// 已上传的文件列表
const attachedFiles = ref<AttachmentsProps['items']>([])
// AI请求加载状态
const agentRequestLoading = ref(false)

// ==================== 流式响应相关状态 ====================
// 当前流式响应的消息ID
const currentStreamingMessageId = ref<string | null>(null)
// 流式响应的累积内容
const streamingContent = ref('')
// 是否正在接收流式数据
const isStreaming = ref(false)

// ==================== 自定义消息管理 ====================
// 自定义消息列表，替代useXChat的消息管理
const customMessages = ref<Array<{
  id: string
  message: string
  status: 'local' | 'ai' | 'loading'
  isStreaming?: boolean
  attachments?: Array<{
    name: string
    size: number
    type: string
    url?: string
  }>
}>>([])

// ==================== 流式请求函数 ====================
// 发送流式请求到后端
async function sendStreamRequest(message: string, attachments?: AttachmentsProps['items']) {
  try {
    // 处理附件信息
    const attachmentInfos = attachments?.map(file => ({
      name: file.name || '',
      size: file.size || 0,
      type: file.type || '',
      url: file.url || ''
    })) || []

    // 创建用户消息
    const userMessageId = `user-${Date.now()}`
    customMessages.value.push({
      id: userMessageId,
      message,
      status: 'local',
      attachments: attachmentInfos.length > 0 ? attachmentInfos : undefined
    })

    // 创建AI消息占位符
    const aiMessageId = `ai-${Date.now()}`
    currentStreamingMessageId.value = aiMessageId
    streamingContent.value = ''
    isStreaming.value = true

    customMessages.value.push({
      id: aiMessageId,
      message: '',
      status: 'loading',
      isStreaming: true
    })

    const chatRequestBody = {
      message: message,
      userId: "user-01",
      sessionId: "session-01",
      attachments: attachmentInfos.length > 0 ? attachmentInfos : undefined
    }

    // 发送POST请求到后端流式接口
    const response = await fetch('http://localhost:8080/api/ai/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/plain',
      },
      body: JSON.stringify(chatRequestBody)
    })
    console.log('response', response)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 获取响应流
    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('No response body reader available')
    }

    const decoder = new TextDecoder()

    // 读取流式数据
    while (true) {
      const {done, value} = await reader.read()

      if (done) {
        break
      }

      // 解码接收到的数据
      const chunk = decoder.decode(value, {stream: true})

      // 累积流式内容
      streamingContent.value += chunk

      // 更新消息内容
      const messageIndex = customMessages.value.findIndex(msg => msg.id === aiMessageId)
      if (messageIndex !== -1) {
        customMessages.value[messageIndex].message = streamingContent.value
        customMessages.value[messageIndex].status = 'ai'
      }
    }

  } catch (error) {
    console.error('Stream request failed:', error)

    // 处理错误，更新消息状态
    if (currentStreamingMessageId.value) {
      const messageIndex = customMessages.value.findIndex(msg => msg.id === currentStreamingMessageId.value)
      if (messageIndex !== -1) {
        customMessages.value[messageIndex].message = '抱歉，请求失败，请重试。'
        customMessages.value[messageIndex].status = 'ai'
        customMessages.value[messageIndex].isStreaming = false
      }
    }
  } finally {
    // 清理流式状态
    isStreaming.value = false
    currentStreamingMessageId.value = null
    streamingContent.value = ''
  }
}

// ==================== 事件处理函数 ====================
// 处理消息提交
async function onSubmit(nextContent: string) {
  if (!nextContent || isStreaming.value) {
    return
  }

  console.log('onSubmit', nextContent)

  // 发送流式请求（包含附件信息）
  await sendStreamRequest(nextContent, attachedFiles.value)

  // 清空输入框和附件
  content.value = ''
  attachedFiles.value = []
  headerOpen.value = false
}

// 处理提示词点击
const onPromptsItemClick: PromptsProps['onItemClick'] = async (info) => {
  const promptText = info.data.description as string
  if (promptText && !isStreaming.value) {
    await sendStreamRequest(promptText)
  }
}

// 添加新对话
function onAddConversation() {
  conversationsItems.value = [
    ...conversationsItems.value,
    {
      key: `${conversationsItems.value.length}`,
      label: `New Conversation ${conversationsItems.value.length}`,
    },
  ]
  activeKey.value = `${conversationsItems.value.length}`
  console.log("conversationsItems", conversationsItems.value)
}

// 处理对话切换
const onConversationClick: ConversationsProps['onActiveChange'] = (key) => {
  activeKey.value = key
  // 切换对话时清空消息列表
  customMessages.value = []
}

// 处理文件变化
const handleFileChange: AttachmentsProps['onChange'] = info => attachedFiles.value = info.fileList

// ==================== 计算属性 ====================
// 欢迎页面节点 - 当没有消息时显示
const placeholderNode = computed(() => h(
    Space,
    {direction: "vertical", size: 16, style: styles.value.placeholder},
    () => [
      // 欢迎组件
      h(
          Welcome,
          {
            variant: "borderless",
            icon: "https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp",
            title: "Hello, I'm Ant Design X",
            description: "Base on Ant Design, AGI product interface solution, create a better intelligent vision~",
            extra: h(Space, {}, () => [h(Button, {icon: h(ShareAltOutlined)}), h(Button, {icon: h(EllipsisOutlined)})]),
          }
      ),
      // 提示词组件
      h(
          Prompts,
          {
            title: "Do you want?",
            items: placeholderPromptsItems,
            styles: {
              list: {
                width: '100%',
              },
              item: {
                flex: 1,
              },
            },
            onItemClick: onPromptsItemClick,
          }
      )
    ]
))

// 消息列表数据 - 根据当前状态动态计算
const items = computed<BubbleListProps['items']>(() => {
  // 如果没有消息，显示欢迎页面
  if (customMessages.value.length === 0) {
    return [{content: placeholderNode, variant: 'borderless'}]
  }

  // 将自定义消息数据转换为气泡组件需要的格式
  return customMessages.value.map(({id, message, status, isStreaming, attachments}) => ({
    key: id,
    loading: status === 'loading', // 加载状态
    role: status === 'local' ? 'local' : 'ai', // 角色判断
    content: status === 'ai'
      ? renderMarkdown(message)
      : status === 'local'
        ? renderUserMessage(message, attachments)
        : message, // 用户消息包含附件信息，AI消息使用Markdown渲染
  }))
})

// ==================== 监听器 ====================
// 监听对话切换，清空消息列表
watch(activeKey, () => {
  customMessages.value = []
}, {immediate: true})
</script>

<style scoped>
/* Markdown样式优化 */
:deep(.markdown-content) {
  line-height: 1.6;
  word-wrap: break-word;
}

/* 代码块样式 */
:deep(.markdown-content pre) {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  margin: 8px 0;
}

:deep(.markdown-content code) {
  background-color: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
}

/* 引用块样式 */
:deep(.markdown-content blockquote) {
  border-left: 4px solid #dfe2e5;
  padding-left: 16px;
  margin: 8px 0;
  color: #6a737d;
}

/* 列表样式 */
:deep(.markdown-content ul),
:deep(.markdown-content ol) {
  padding-left: 20px;
  margin: 8px 0;
}

/* 链接样式 */
:deep(.markdown-content a) {
  color: #0366d6;
  text-decoration: none;
}

:deep(.markdown-content a:hover) {
  text-decoration: underline;
}

/* 表格样式 */
:deep(.markdown-content table) {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

:deep(.markdown-content th),
:deep(.markdown-content td) {
  border: 1px solid #dfe2e5;
  padding: 8px 12px;
  text-align: left;
}

:deep(.markdown-content th) {
  background-color: #f6f8fa;
  font-weight: 600;
}
</style>

