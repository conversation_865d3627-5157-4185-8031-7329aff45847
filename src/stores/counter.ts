import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// 计数器状态管理
// 这是一个示例store，展示Pinia的基本用法
export const useCounterStore = defineStore('counter', () => {
  // 计数器值
  const count = ref(0)
  
  // 计算属性：计数器值的两倍
  const doubleCount = computed(() => count.value * 2)
  
  // 增加计数器的方法
  function increment() {
    count.value++
  }

  // 返回状态和方法，供组件使用
  return { count, doubleCount, increment }
})
