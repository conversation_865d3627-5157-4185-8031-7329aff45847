// 文件上传API接口

export interface UploadResponse {
  success: boolean
  message: string
  data?: {
    url: string
    filename: string
    size: number
    type: string
  }
}

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

/**
 * 上传单个文件到服务器
 * @param file 要上传的文件
 * @param onProgress 上传进度回调
 * @returns Promise<UploadResponse>
 */
export async function uploadFile(
  file: File, 
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResponse> {
  return new Promise((resolve, reject) => {
    const formData = new FormData()
    formData.append('file', file)

    const xhr = new XMLHttpRequest()

    // 监听上传进度
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress: UploadProgress = {
            loaded: event.loaded,
            total: event.total,
            percentage: Math.round((event.loaded / event.total) * 100)
          }
          onProgress(progress)
        }
      })
    }

    // 监听请求完成
    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response: UploadResponse = JSON.parse(xhr.responseText)
          resolve(response)
        } catch (error) {
          reject(new Error('解析响应失败'))
        }
      } else {
        reject(new Error(`上传失败: ${xhr.status} ${xhr.statusText}`))
      }
    })

    // 监听请求错误
    xhr.addEventListener('error', () => {
      reject(new Error('网络错误'))
    })

    // 监听请求超时
    xhr.addEventListener('timeout', () => {
      reject(new Error('请求超时'))
    })

    // 配置请求
    xhr.timeout = 30000 // 30秒超时
    xhr.open('POST', 'http://localhost:8080/api/upload/file')
    
    // 发送请求
    xhr.send(formData)
  })
}

/**
 * 批量上传文件
 * @param files 要上传的文件列表
 * @param onProgress 整体进度回调
 * @param onFileProgress 单个文件进度回调
 * @returns Promise<UploadResponse[]>
 */
export async function uploadFiles(
  files: File[],
  onProgress?: (completed: number, total: number) => void,
  onFileProgress?: (fileIndex: number, progress: UploadProgress) => void
): Promise<UploadResponse[]> {
  const results: UploadResponse[] = []
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    
    try {
      const result = await uploadFile(file, (progress) => {
        if (onFileProgress) {
          onFileProgress(i, progress)
        }
      })
      
      results.push(result)
      
      if (onProgress) {
        onProgress(i + 1, files.length)
      }
    } catch (error) {
      console.error(`文件 ${file.name} 上传失败:`, error)
      results.push({
        success: false,
        message: error instanceof Error ? error.message : '上传失败'
      })
    }
  }
  
  return results
}

/**
 * 验证文件类型和大小
 * @param file 要验证的文件
 * @param maxSize 最大文件大小（字节），默认10MB
 * @param allowedTypes 允许的文件类型，默认允许所有类型
 * @returns 验证结果
 */
export function validateFile(
  file: File,
  maxSize: number = 10 * 1024 * 1024, // 10MB
  allowedTypes?: string[]
): { valid: boolean; message?: string } {
  // 检查文件大小
  if (file.size > maxSize) {
    return {
      valid: false,
      message: `文件大小不能超过 ${formatFileSize(maxSize)}`
    }
  }

  // 检查文件类型
  if (allowedTypes && allowedTypes.length > 0) {
    const fileType = file.type
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    
    const isTypeAllowed = allowedTypes.some(type => {
      if (type.startsWith('.')) {
        // 扩展名匹配
        return type.slice(1) === fileExtension
      } else {
        // MIME类型匹配
        return fileType.startsWith(type)
      }
    })

    if (!isTypeAllowed) {
      return {
        valid: false,
        message: `不支持的文件类型。支持的类型: ${allowedTypes.join(', ')}`
      }
    }
  }

  return { valid: true }
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}
