# Ant Design X Vue AI聊天项目

这是一个基于 **Ant Design X Vue** 的AI聊天界面演示项目，展示了如何使用 `ant-design-x-vue` 组件库构建现代化的AI对话应用。

## 🚀 项目特点

- **现代化UI**: 基于 Ant Design Vue 4.x 的现代化界面设计
- **AI组件集成**: 完整集成 `ant-design-x-vue` AI组件库
- **TypeScript支持**: 完整的TypeScript类型支持
- **响应式设计**: 适配不同屏幕尺寸
- **主题支持**: 支持明暗主题切换

## 📦 核心依赖

### 主要依赖
- **@ant-design/x**: Ant Design X 核心库 (^1.6.0)
- **ant-design-vue**: Ant Design Vue 组件库 (^4.2.6)  
- **ant-design-x-vue**: Ant Design X Vue AI组件库 (^1.3.2)
- **vue**: Vue 3框架 (^3.5.18)
- **pinia**: Vue状态管理 (^3.0.3)
- **vue-router**: Vue路由 (^4.5.1)

### 开发依赖
- **vite**: 构建工具 (^7.0.6)
- **typescript**: TypeScript编译器 (~5.8.0)
- **@vitejs/plugin-vue**: Vite Vue插件 (^6.0.1)

## 🎯 AI组件功能

### 通用组件
- **Bubble**: 对话气泡组件，支持AI和用户消息展示
- **Conversations**: 对话管理组件，支持多会话切换

### 唤醒组件  
- **Welcome**: 欢迎界面组件，引导用户开始对话
- **Prompts**: 提示词组件，提供预设对话选项

### 表达组件
- **Sender**: 消息输入组件，支持文本输入
- **Attachments**: 文件附件组件，支持文件上传
- **Suggestion**: 快捷指令组件，提供操作建议

### 工具组件
- **useXAgent**: AI模型调度组合式函数
- **useXChat**: 聊天数据管理组合式函数
- **XStream**: 流式数据传输工具
- **XRequest**: AI服务请求工具
- **XProvider**: 全局配置提供者

## 🛠️ 开发命令

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 类型检查
npm run type-check
```

## 📁 项目结构

```
src/
├── components/
│   └── ai/
│       └── index.vue          # 主AI聊天组件
├── stores/
│   └── counter.ts             # 示例状态管理
├── router/
│   └── index.ts               # 路由配置
├── App.vue                    # 根组件
└── main.ts                    # 应用入口
```

## 🎨 界面功能

### 左侧菜单栏
- Logo展示
- 新建对话按钮
- 对话列表管理

### 主聊天区域
- 消息气泡展示
- 欢迎界面
- 提示词选择

### 输入区域
- 消息输入框
- 文件附件功能
- 快捷提示词

## 🔧 技术实现

### 状态管理
- 使用 `useXAgent` 管理AI代理
- 使用 `useXChat` 管理聊天数据
- 使用 `ref` 和 `computed` 管理响应式状态

### 样式系统
- 基于 Ant Design 主题系统
- 响应式布局设计
- 动态样式计算

### 组件通信
- 事件驱动架构
- Props/Emits 组件通信
- 组合式API状态共享

## 📚 学习资源

- [Ant Design X Vue 官方文档](https://antd-design-x-vue.netlify.app/)
- [Ant Design Vue 官方文档](https://antdv.com/)
- [Vue 3 官方文档](https://vuejs.org/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## �� 许可证

MIT License
