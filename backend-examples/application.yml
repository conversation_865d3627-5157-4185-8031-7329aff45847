# Spring Boot 配置文件
server:
  port: 8080

spring:
  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB      # 单个文件最大大小
      max-request-size: 50MB   # 整个请求最大大小
      file-size-threshold: 2KB # 文件写入磁盘的阈值

  # 跨域配置
  web:
    cors:
      allowed-origins: "http://localhost:5173"
      allowed-methods: "*"
      allowed-headers: "*"
      allow-credentials: true

# 自定义文件上传配置
file:
  upload:
    path: ./uploads/          # 文件上传路径
    max-size: 10485760       # 最大文件大小（10MB）

# 日志配置
logging:
  level:
    com.example.ai: DEBUG
    org.springframework.web: DEBUG
