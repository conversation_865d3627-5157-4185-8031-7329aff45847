package com.example.ai.controller;

import com.example.ai.dto.ChatRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.List;

/**
 * AI聊天控制器 - 支持文件附件
 */
@RestController
@RequestMapping("/api/ai")
@CrossOrigin(origins = "http://localhost:5173")
public class AiController {

    /**
     * 流式聊天接口 - 支持文件附件
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_PLAIN_VALUE)
    public Flux<String> streamChat(@RequestBody ChatRequest request) {
        
        // 打印接收到的请求信息
        System.out.println("收到聊天请求: " + request);
        
        // 处理附件信息
        if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
            System.out.println("包含 " + request.getAttachments().size() + " 个附件:");
            for (ChatRequest.AttachmentInfo attachment : request.getAttachments()) {
                System.out.println("  - 文件: " + attachment.getName() + 
                                 ", 大小: " + attachment.getSize() + 
                                 ", 类型: " + attachment.getType() + 
                                 ", URL: " + attachment.getUrl());
            }
        }

        // 模拟AI响应生成
        return generateAiResponse(request);
    }

    /**
     * 生成AI响应（模拟）
     */
    private Flux<String> generateAiResponse(ChatRequest request) {
        String userMessage = request.getMessage();
        List<ChatRequest.AttachmentInfo> attachments = request.getAttachments();
        
        // 构建响应内容
        StringBuilder responseBuilder = new StringBuilder();
        
        // 基本回复
        responseBuilder.append("我收到了您的消息：\"").append(userMessage).append("\"");
        
        // 如果有附件，添加附件相关的回复
        if (attachments != null && !attachments.isEmpty()) {
            responseBuilder.append("\n\n我还看到您上传了 ").append(attachments.size()).append(" 个文件：\n");
            for (int i = 0; i < attachments.size(); i++) {
                ChatRequest.AttachmentInfo attachment = attachments.get(i);
                responseBuilder.append((i + 1)).append(". ")
                              .append(attachment.getName())
                              .append(" (").append(formatFileSize(attachment.getSize())).append(")\n");
            }
            responseBuilder.append("\n我已经接收到这些文件，可以根据文件内容为您提供帮助。");
        }
        
        responseBuilder.append("\n\n这是一个模拟的AI响应。在实际应用中，这里会调用真正的AI模型来生成回复。");
        
        String fullResponse = responseBuilder.toString();
        
        // 将响应分割成小块，模拟流式传输
        return Flux.fromArray(fullResponse.split(""))
                   .delayElements(Duration.ofMillis(50)) // 每50ms发送一个字符
                   .doOnNext(chunk -> System.out.print(chunk))
                   .doOnComplete(() -> System.out.println("\n流式传输完成"));
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(Long bytes) {
        if (bytes == null || bytes == 0) return "0 B";
        
        String[] sizes = {"B", "KB", "MB", "GB", "TB"};
        int i = (int) Math.floor(Math.log(bytes) / Math.log(1024));
        
        return Math.round(bytes / Math.pow(1024, i) * 100.0) / 100.0 + " " + sizes[i];
    }

    /**
     * 非流式聊天接口（可选）
     */
    @PostMapping("/chat")
    public String chat(@RequestBody ChatRequest request) {
        System.out.println("收到非流式聊天请求: " + request);
        
        StringBuilder response = new StringBuilder();
        response.append("收到消息: ").append(request.getMessage());
        
        if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
            response.append("\n附件数量: ").append(request.getAttachments().size());
        }
        
        return response.toString();
    }
}
