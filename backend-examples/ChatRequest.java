package com.example.ai.dto;

import java.util.List;

/**
 * 聊天请求DTO
 */
public class ChatRequest {
    private String message;
    private String userId;
    private String sessionId;
    private List<AttachmentInfo> attachments;

    // 构造函数
    public ChatRequest() {}

    public ChatRequest(String message, String userId, String sessionId) {
        this.message = message;
        this.userId = userId;
        this.sessionId = sessionId;
    }

    // Getter和Setter方法
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public List<AttachmentInfo> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<AttachmentInfo> attachments) {
        this.attachments = attachments;
    }

    @Override
    public String toString() {
        return "ChatRequest{" +
                "message='" + message + '\'' +
                ", userId='" + userId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", attachments=" + attachments +
                '}';
    }

    /**
     * 附件信息内部类
     */
    public static class AttachmentInfo {
        private String url;
        private String name;
        private Long size;
        private String type;

        // 构造函数
        public AttachmentInfo() {}

        public AttachmentInfo(String url, String name, Long size, String type) {
            this.url = url;
            this.name = name;
            this.size = size;
            this.type = type;
        }

        // Getter和Setter方法
        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Long getSize() {
            return size;
        }

        public void setSize(Long size) {
            this.size = size;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        @Override
        public String toString() {
            return "AttachmentInfo{" +
                    "url='" + url + '\'' +
                    ", name='" + name + '\'' +
                    ", size=" + size +
                    ", type='" + type + '\'' +
                    '}';
        }
    }
}
